# MySQL 配置项
# 这是预发环境！！！！
CHATBI_DB_HOST=rm-bp1w08un1w996pv2t.mysql.rds.aliyuncs.com
CHATBI_DB_PORT=3306
CHATBI_DB_USER=xianmu_ai
CHATBI_DB_PASSWORD=T8m$pL9!
CHATBI_DB_NAME=chatbi
# 超时时间：秒数
CHATBI_CONNECT_TIMEOUT=2

# --- Model Provider Configuration ---
# 鲜沐内部API配置
PROVIDER_XM_API_KEY=sk-iOKpa0SLddBMt0BsRH-DYA
PROVIDER_XM_API_BASE=https://litellm.summerfarm.net/v1
PROVIDER_XM_DEFAULT_MODEL=kimi-k2-turbo
PROVIDER_XM_FAST_MODEL=kimi-k2-turbo

# OpenRouter API配置
PROVIDER_OPENROUTER_API_KEY=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
PROVIDER_OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PROVIDER_OPENROUTER_DEFAULT_MODEL=google/gemini-2.5-pro
PROVIDER_OPENROUTER_FAST_MODEL=openai/gpt-oss-120b
PROVIDER_OPENROUTER_CLAUDE_MODEL=anthropic/claude-sonnet-4

# 默认提供者配置
DEFAULT_MODEL_PROVIDER=openrouter

# --- RAG Configuration ---
TOP_TABLE_CNT=20

# --- ChatBI Configuration ---
CHAT_BI_HOST_NAME=https://prechat-bi.summerfarm.net

# --- Flask Configuration (Optional) ---
FLASK_ENV=production # Set to 'production' for deployment
FLASK_DEBUG=0 # Enable debug mode (1) or disable (0)

# SQL返回的行数大于这个值时，默认上传到飞书
MIN_ROWS_TO_IMPORT=20

# 强制刷新token的时间间隔（分钟）
MINUTES_TO_FORCE_REFRESH_TOKEN=10

# 飞书应用的 App ID 和 App Secret(预发环境和正式环境一样)
FEISHU_APP_ID=cli_a7d55bc7cb7cd00b
FEISHU_APP_SECRET=S95m7GmcGhteLaaOImj7Uf0GkESpyig0

# 是否启用飞书消息处理(预发环境设置为false)
ENABLE_BOT_MESSAGE_PROCESSING=false

# 应用根目录(解决nginx部署问题)
APPLICATION_ROOT=

# 如果超过400行，则对喂给AI的数据进行截断
MIN_ROWS_TO_CUTOFF=400

# 如果超过20行，则自动上传到飞书多维表格
MIN_ROWS_TO_IMPORT=20

# 飞书卡片超时时间，超过这个时间后就不可更新了（可用于调整测试）
STREAM_TIMEOUT_MINUTES=10

# Bad Case通知群聊ID（可选）
# 当用户标记对话为Bad Case时，会发送通知到此群聊
# 如果不配置此项，则不会发送通知
BAD_CASE_NOTIFICATION_CHAT_ID=oc_61c2a621535eacb60c33711c09231781
GOOD_CASE_NOTIFICATION_CHAT_ID=oc_61c2a621535eacb60c33711c09231781,oc_1872d07c2c63342a1458f34e8ea47281

# 群聊时，@机器人的名称
FEISHU_BOT_NAME=鲜沐ChatBI

# 当用户进入和机器人进行P2P聊天时，如果距离上次发送消息的时间小于这个值（单位：小时），则不发送欢迎消息
USER_ENTERED_THRESHOLD=24

# 是否开启model跟踪，本地环境可以开启，线上环境尽量不要开启
OUTPUT_MODEL_PROVIDER=false

ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc
ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************

# 是否启用后台任务
ENABLE_BACKGROUND_TASK=false